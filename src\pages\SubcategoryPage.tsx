import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { ChevronRight, Filter, Grid, List } from 'lucide-react';
import ProductCard from '../components/ProductCard';
import { fetchProductsBySubcategory, fetchCategories, fetchSubcategories } from '../lib/api';
import { Product, Category, Subcategory } from '../types';
import { useCart } from '../context/CartContext';

const SubcategoryPage: React.FC = () => {
  const { subcategoryId } = useParams<{ subcategoryId: string }>();
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortOption, setSortOption] = useState<string>('default');
  const [showFilters, setShowFilters] = useState(false);
  const [loading, setLoading] = useState(true);
  const [productsLoading, setProductsLoading] = useState(true);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [priceFilter, setPriceFilter] = useState<string | null>(null);
  const [availabilityFilter, setAvailabilityFilter] = useState<string | null>(null);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const { addToCart } = useCart();

  const subcategory = subcategories.find(sub => sub.id === subcategoryId);
  const category = subcategory ? categories.find(cat => cat.id === subcategory.category_id) : null;

  // Fetch categories and subcategories
  useEffect(() => {
    const loadData = async () => {
      try {
        setCategoriesLoading(true);
        const categoriesData = await fetchCategories();
        setCategories(categoriesData);

        if (subcategoryId) {
          const allSubcategories: Subcategory[] = [];

          // Fetch subcategories for each category
          for (const category of categoriesData) {
            const subcats = await fetchSubcategories(category.id);
            allSubcategories.push(...subcats);
          }

          setSubcategories(allSubcategories);
        }
      } catch (err) {
        console.error('Error loading categories and subcategories:', err);
        setError('Failed to load categories. Please try again later.');
      } finally {
        setCategoriesLoading(false);
      }
    };

    loadData();
  }, [subcategoryId]);

  // Fetch products by subcategory
  useEffect(() => {
    const loadProducts = async () => {
      if (subcategoryId) {
        try {
          setProductsLoading(true);
          let data = await fetchProductsBySubcategory(subcategoryId);

          // Apply sorting
          switch (sortOption) {
            case 'price-low':
              data = [...data].sort((a, b) => a.price - b.price);
              break;
            case 'price-high':
              data = [...data].sort((a, b) => b.price - a.price);
              break;
            case 'name-asc':
              data = [...data].sort((a, b) => a.name.localeCompare(b.name));
              break;
            default:
              // Default sort or no sort
              break;
          }

          setProducts(data);
          setError(null);
        } catch (err) {
          console.error(`Error loading products for subcategory ${subcategoryId}:`, err);
          setError('Failed to load products. Please try again later.');
        } finally {
          setProductsLoading(false);
        }
      }
    };

    loadProducts();
  }, [subcategoryId, sortOption]);

  // Update main loading state based on both categories and products loading
  useEffect(() => {
    setLoading(categoriesLoading || productsLoading);
  }, [categoriesLoading, productsLoading]);

  // Apply filters whenever products, priceFilter, or availabilityFilter changes
  useEffect(() => {
    let filtered = [...products];

    // Apply price filter
    if (priceFilter) {
      switch (priceFilter) {
        case 'under-100':
          filtered = filtered.filter(product => product.price < 100);
          break;
        case '100-200':
          filtered = filtered.filter(product => product.price >= 100 && product.price <= 200);
          break;
        case 'above-200':
          filtered = filtered.filter(product => product.price > 200);
          break;
      }
    }

    // Apply availability filter
    if (availabilityFilter) {
      switch (availabilityFilter) {
        case 'in-stock':
          filtered = filtered.filter(product =>
            product.is_available && (product.stock_quantity > 0 || product.stock > 0)
          );
          break;
        case 'out-of-stock':
          filtered = filtered.filter(product =>
            !product.is_available || product.stock_quantity <= 0 || product.stock <= 0
          );
          break;
      }
    }

    setFilteredProducts(filtered);
  }, [products, priceFilter, availabilityFilter]);

  const handlePriceFilter = (filter: string) => {
    setPriceFilter(priceFilter === filter ? null : filter);
  };

  const handleAvailabilityFilter = (filter: string) => {
    setAvailabilityFilter(availabilityFilter === filter ? null : filter);
  };

  const clearFilters = () => {
    setPriceFilter(null);
    setAvailabilityFilter(null);
  };

  // Show loading while categories and subcategories are being fetched
  if (categoriesLoading || (!subcategory && categories.length === 0)) {
    return (
      <div className="min-h-screen pt-16 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // Show not found only after categories are loaded and subcategory is still not found
  if (!subcategory && categories.length > 0 && !categoriesLoading) {
    return (
      <div className="min-h-screen pt-16 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-800">Subcategory not found</h2>
          <p className="mt-2 text-gray-600">The subcategory you're looking for doesn't exist.</p>
          <Link to="/categories" className="mt-4 inline-block text-primary-600 hover:text-primary-700">
            Browse all categories
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-16">
      <div
        className="bg-primary-50 bg-opacity-80 py-8"
        style={{
          backgroundImage: `url(${subcategory.image_url})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundBlendMode: 'overlay'
        }}
      >
        <div className="container mx-auto px-4">
          <div className="flex items-center text-sm text-gray-600 mb-2">
            <Link to="/" className="hover:text-primary-600">Home</Link>
            <ChevronRight className="w-4 h-4 mx-1" />
            <Link to="/categories" className="hover:text-primary-600">Categories</Link>
            {category && (
              <>
                <ChevronRight className="w-4 h-4 mx-1" />
                <Link to={`/category/${category.id}`} className="hover:text-primary-600">{category.name}</Link>
              </>
            )}
            <ChevronRight className="w-4 h-4 mx-1" />
            <span className="text-gray-800">{subcategory.name}</span>
          </div>

          <h1 className="text-3xl font-bold text-gray-800">{subcategory.name}</h1>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0 mb-8">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-2 text-gray-700 hover:text-primary-600"
          >
            <Filter className="w-5 h-5" />
            <span>Filter & Sort</span>
          </button>

          <div className="flex items-center space-x-4">
            <div>
              <select
                value={sortOption}
                onChange={(e) => setSortOption(e.target.value)}
                className="p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="default">Sort by: Default</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="name-asc">Name: A to Z</option>
              </select>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md ${viewMode === 'grid' ? 'bg-primary-100 text-primary-700' : 'bg-gray-100 text-gray-600'}`}
              >
                <Grid className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md ${viewMode === 'list' ? 'bg-primary-100 text-primary-700' : 'bg-gray-100 text-gray-600'}`}
              >
                <List className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {showFilters && (
          <div className="bg-gray-50 p-4 rounded-lg mb-6 border border-gray-200">
            <h3 className="text-lg font-medium text-gray-800 mb-3">Filters</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-700 mb-2">Price Range</p>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handlePriceFilter('under-100')}
                    className={`px-3 py-1 border rounded-md text-sm transition-colors ${
                      priceFilter === 'under-100'
                        ? 'bg-primary-500 text-white border-primary-500'
                        : 'border-gray-300 hover:bg-primary-50 hover:border-primary-300'
                    }`}
                  >
                    Under ₹100
                  </button>
                  <button
                    onClick={() => handlePriceFilter('100-200')}
                    className={`px-3 py-1 border rounded-md text-sm transition-colors ${
                      priceFilter === '100-200'
                        ? 'bg-primary-500 text-white border-primary-500'
                        : 'border-gray-300 hover:bg-primary-50 hover:border-primary-300'
                    }`}
                  >
                    ₹100 - ₹200
                  </button>
                  <button
                    onClick={() => handlePriceFilter('above-200')}
                    className={`px-3 py-1 border rounded-md text-sm transition-colors ${
                      priceFilter === 'above-200'
                        ? 'bg-primary-500 text-white border-primary-500'
                        : 'border-gray-300 hover:bg-primary-50 hover:border-primary-300'
                    }`}
                  >
                    Above ₹200
                  </button>
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-gray-700 mb-2">Availability</p>
                <div className="space-x-4">
                  <label className="inline-flex items-center">
                    <input
                      type="checkbox"
                      checked={availabilityFilter === 'in-stock'}
                      onChange={() => handleAvailabilityFilter('in-stock')}
                      className="form-checkbox text-primary-600"
                    />
                    <span className="ml-2 text-sm text-gray-700">In Stock</span>
                  </label>
                  <label className="inline-flex items-center">
                    <input
                      type="checkbox"
                      checked={availabilityFilter === 'out-of-stock'}
                      onChange={() => handleAvailabilityFilter('out-of-stock')}
                      className="form-checkbox text-primary-600"
                    />
                    <span className="ml-2 text-sm text-gray-700">Out of Stock</span>
                  </label>
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <button
                  onClick={clearFilters}
                  className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
                >
                  Clear Filters
                </button>
                <button
                  onClick={() => setShowFilters(false)}
                  className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
                >
                  Apply Filters
                </button>
              </div>
            </div>
          </div>
        )}

        {productsLoading && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
          </div>
        )}

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {!productsLoading && !error && filteredProducts.length === 0 && products.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No products found in this subcategory</p>
            <Link to="/categories" className="mt-4 inline-block text-primary-600 hover:text-primary-700">
              Browse other categories
            </Link>
          </div>
        ) : !productsLoading && !error && filteredProducts.length === 0 && products.length > 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No products match the selected filters</p>
            <button
              onClick={clearFilters}
              className="mt-4 inline-block text-primary-600 hover:text-primary-700"
            >
              Clear filters to see all products
            </button>
          </div>
        ) : !productsLoading && !error && viewMode === 'grid' ? (
          <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
            {filteredProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        ) : !productsLoading && !error ? (
          <div className="space-y-4">
            {filteredProducts.map((product) => {
              // Check if product is out of stock (same logic as ProductCard)
              const isOutOfStock = !product.is_available ||
                (product.stock_quantity !== undefined && product.stock_quantity <= 0) ||
                (product.stock !== undefined && product.stock <= 0);

              // Get available stock
              const availableStock = product.is_available ?
                (product.stock_quantity !== undefined ? product.stock_quantity : product.stock || 0) : 0;

              return (
                <div key={product.id} className="flex border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow bg-white">
                  <div className="relative w-32 h-32 flex-shrink-0">
                    <img
                      src={product.image || product.image_url}
                      alt={product.name}
                      className="w-full h-full object-cover"
                    />

                    {/* Out of Stock Overlay */}
                    {isOutOfStock && (
                      <div className="absolute top-0 right-0 left-0 bg-red-500 text-white text-center py-1 text-xs">
                        Out of Stock
                      </div>
                    )}

                    {/* Low Stock Overlay */}
                    {!isOutOfStock && availableStock <= 10 && (
                      <div className="absolute top-0 right-0 left-0 bg-orange-500 text-white text-center py-1 text-xs">
                        Low Stock: Only {availableStock} left
                      </div>
                    )}
                  </div>

                  <div className="p-4 flex-1 flex flex-col">
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <h3 className="text-lg font-medium text-gray-800">{product.name}</h3>
                        <span className="text-xs text-gray-500">{product.unit || '1 unit'}</span>
                      </div>
                      <p className="text-gray-600 text-sm mt-1">{product.description}</p>

                      {/* Stock information for list view */}
                      {!isOutOfStock && availableStock <= 10 && (
                        <p className="text-orange-600 text-xs mt-2">Only {availableStock} left in stock</p>
                      )}
                    </div>

                    <div className="flex justify-between items-center mt-4">
                      <p className="text-primary-600 font-bold">₹{product.price}</p>

                      {/* Add to Cart Button with Out of Stock handling */}
                      {isOutOfStock ? (
                        <button
                          disabled
                          className="px-4 py-2 bg-gray-200 text-gray-500 rounded-md cursor-not-allowed"
                        >
                          Out of Stock
                        </button>
                      ) : (
                        <button
                          onClick={() => addToCart(product)}
                          className="px-4 py-2 bg-primary-100 hover:bg-primary-200 text-primary-700 rounded-md transition-colors"
                        >
                          Add to Cart
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default SubcategoryPage;
