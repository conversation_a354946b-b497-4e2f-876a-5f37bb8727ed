import { supabase } from './supabase';
import { Category, Product, Subcategory, CartItem, Order, OrderItem } from '../types';

// Fetch all categories
export async function fetchCategories(): Promise<Category[]> {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching categories:', error);
      return [];
    }

    // Add a description field for backward compatibility
    const categoriesWithDescription = data?.map(category => ({
      ...category,
      description: 'Category description',
      image: category.image_url // For backward compatibility
    })) || [];

    return categoriesWithDescription;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

// Fetch subcategories by category ID
export async function fetchSubcategories(categoryId: string): Promise<Subcategory[]> {
  try {
    const { data, error } = await supabase
      .from('subcategories')
      .select('*')
      .eq('category_id', categoryId)
      .order('name');

    if (error) {
      console.error(`Error fetching subcategories for category ${categoryId}:`, error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error(`Error fetching subcategories for category ${categoryId}:`, error);
    return [];
  }
}

// Fetch all subcategories
export async function fetchAllSubcategories(): Promise<Subcategory[]> {
  try {
    const { data, error } = await supabase
      .from('subcategories')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching all subcategories:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching all subcategories:', error);
    return [];
  }
}

// Fetch products by subcategory
export async function fetchProductsBySubcategory(subcategoryId: string): Promise<Product[]> {
  try {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('subcategory_id', subcategoryId)
      .order('name');

    if (error) {
      console.error(`Error fetching products for subcategory ${subcategoryId}:`, error);
      return [];
    }

    // Transform data to match the expected format
    const transformedProducts = data?.map(product => ({
      ...product,
      category: '', // Will be filled in by the component if needed
      unit: '1 unit', // Default unit for backward compatibility
      image: product.image_url, // For backward compatibility
      stock: product.stock_quantity // For backward compatibility
    })) || [];

    return transformedProducts;
  } catch (error) {
    console.error(`Error fetching products for subcategory ${subcategoryId}:`, error);
    return [];
  }
}

// Fetch all products
export async function fetchProducts(): Promise<Product[]> {
  try {
    const { data, error } = await supabase
      .from('products')
      .select('*, subcategories(*, categories(*))')
      .order('name');

    if (error) {
      console.error('Error fetching products:', error);
      return [];
    }

    // Transform data to match the expected format
    const transformedProducts = data?.map(product => {
      const subcategory = product.subcategories as any;
      const category = subcategory?.categories as any;

      return {
        ...product,
        category: category?.id || '', // For backward compatibility
        unit: '1 unit', // Default unit for backward compatibility
        image: product.image_url, // For backward compatibility
        stock: product.stock_quantity // For backward compatibility
      };
    }) || [];

    return transformedProducts;
  } catch (error) {
    console.error('Error fetching products:', error);
    return [];
  }
}

// Fetch products by category
export async function fetchProductsByCategory(categoryId: string): Promise<Product[]> {
  try {
    // First get all subcategories for this category
    const { data: subcategories, error: subcatError } = await supabase
      .from('subcategories')
      .select('id')
      .eq('category_id', categoryId);

    if (subcatError) {
      console.error(`Error fetching subcategories for category ${categoryId}:`, subcatError);
      return [];
    }

    if (!subcategories || subcategories.length === 0) {
      return [];
    }

    // Get all products that belong to these subcategories
    const subcategoryIds = subcategories.map(subcat => subcat.id);
    const { data, error } = await supabase
      .from('products')
      .select('*, subcategories(*, categories(*))')
      .in('subcategory_id', subcategoryIds)
      .order('name');

    if (error) {
      console.error(`Error fetching products for category ${categoryId}:`, error);
      return [];
    }

    // Transform data to match the expected format
    const transformedProducts = data?.map(product => {
      const subcategory = product.subcategories as any;
      const category = subcategory?.categories as any;

      return {
        ...product,
        category: category?.id || '', // For backward compatibility
        unit: '1 unit', // Default unit for backward compatibility
        image: product.image_url, // For backward compatibility
        stock: product.stock_quantity // For backward compatibility
      };
    }) || [];

    return transformedProducts;
  } catch (error) {
    console.error(`Error fetching products for category ${categoryId}:`, error);
    return [];
  }
}

// Search products
export async function searchProducts(query: string): Promise<Product[]> {
  try {
    const { data, error } = await supabase
      .from('products')
      .select('*, subcategories(*, categories(*))')
      .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
      .order('name');

    if (error) {
      console.error(`Error searching products with query ${query}:`, error);
      return [];
    }

    // Transform data to match the expected format
    const transformedProducts = data?.map(product => {
      const subcategory = product.subcategories as any;
      const category = subcategory?.categories as any;

      return {
        ...product,
        category: category?.id || '', // For backward compatibility
        unit: '1 unit', // Default unit for backward compatibility
        image: product.image_url, // For backward compatibility
        stock: product.stock_quantity // For backward compatibility
      };
    }) || [];

    return transformedProducts;
  } catch (error) {
    console.error(`Error searching products with query ${query}:`, error);
    return [];
  }
}

// Fetch a single product by ID
export async function fetchProductById(productId: string): Promise<Product | null> {
  try {
    const { data, error } = await supabase
      .from('products')
      .select('*, subcategories(*, categories(*))')
      .eq('id', productId)
      .single();

    if (error) {
      console.error(`Error fetching product ${productId}:`, error);
      return null;
    }

    if (!data) return null;

    // Transform data to match the expected format
    const subcategory = data.subcategories as any;
    const category = subcategory?.categories as any;

    const transformedProduct = {
      ...data,
      category: category?.id || '', // For backward compatibility
      unit: '1 unit', // Default unit for backward compatibility
      image: data.image_url, // For backward compatibility
      stock: data.stock_quantity // For backward compatibility
    };

    return transformedProduct;
  } catch (error) {
    console.error(`Error fetching product ${productId}:`, error);
    return null;
  }
}

// Update product stock quantities after order placement
async function updateProductStock(cartItems: CartItem[]): Promise<{ success: boolean; message: string }> {
  try {
    // Update stock for each product in the cart
    for (const item of cartItems) {
      const { data: currentProduct, error: fetchError } = await supabase
        .from('products')
        .select('stock_quantity, is_available')
        .eq('id', item.product.id)
        .single();

      if (fetchError) {
        console.error(`Error fetching current stock for product ${item.product.id}:`, fetchError);
        return { success: false, message: `Failed to check stock for ${item.product.name}` };
      }

      if (!currentProduct) {
        return { success: false, message: `Product ${item.product.name} not found` };
      }

      // Check if we have enough stock
      if (currentProduct.stock_quantity < item.quantity) {
        return {
          success: false,
          message: `Insufficient stock for ${item.product.name}. Only ${currentProduct.stock_quantity} available.`
        };
      }

      // Calculate new stock quantity
      const newStockQuantity = currentProduct.stock_quantity - item.quantity;
      const shouldMarkUnavailable = newStockQuantity <= 0;

      // Update the product stock
      const { error: updateError } = await supabase
        .from('products')
        .update({
          stock_quantity: newStockQuantity,
          is_available: shouldMarkUnavailable ? false : currentProduct.is_available,
          updated_at: new Date().toISOString()
        })
        .eq('id', item.product.id);

      if (updateError) {
        console.error(`Error updating stock for product ${item.product.id}:`, updateError);
        return { success: false, message: `Failed to update stock for ${item.product.name}` };
      }

      console.log(`Updated stock for ${item.product.name}: ${currentProduct.stock_quantity} -> ${newStockQuantity}`);
    }

    return { success: true, message: 'Stock updated successfully' };
  } catch (error) {
    console.error('Error updating product stock:', error);
    return { success: false, message: 'Failed to update product stock' };
  }
}

// Create a new order and order items
export async function createOrder(
  customerId: string,
  addressId: string,
  cartItems: CartItem[],
  paymentMethod: string,
  totalAmount: number
): Promise<{ success: boolean; orderId?: string; message: string }> {
  try {
    // Map frontend payment methods to database-accepted values
    // The database has a constraint that only allows specific values: 'COD' and 'ONLINE'
    let dbPaymentMethod: string;
    let paymentStatus: string;

    // Simplify to just use 'COD' or 'ONLINE' as these are the only allowed values
    if (paymentMethod === 'cod') {
      dbPaymentMethod = 'COD';
      paymentStatus = 'pending';
    } else {
      // For all online payment methods (razorpay, upi, card, etc.)
      dbPaymentMethod = 'ONLINE';
      paymentStatus = 'awaiting_payment';
    }

    console.log('Using payment method:', dbPaymentMethod, 'for frontend method:', paymentMethod);

    // Create the order first
    const order: Order = {
      customer_id: customerId,
      address_id: addressId,
      payment_method: dbPaymentMethod,
      total_amount: totalAmount,
      payment_status: paymentStatus,
      status: 'New'
    };

    // Insert the order into the database
    const { data: orderData, error: orderError } = await supabase
      .from('orders')
      .insert([order])
      .select();

    if (orderError) {
      console.error('Error creating order:', orderError);
      return {
        success: false,
        message: 'Failed to create order. Please try again.'
      };
    }

    if (!orderData || orderData.length === 0) {
      return {
        success: false,
        message: 'Failed to create order. No order data returned.'
      };
    }

    const newOrderId = orderData[0].id;

    // Create order items
    const orderItems: OrderItem[] = cartItems.map(item => ({
      order_id: newOrderId,
      product_name: item.product.name,
      product_description: item.product.description,
      product_image: item.product.image_url,
      quantity: item.quantity,
      price: item.product.price
    }));

    // Insert order items into the database
    const { error: itemsError } = await supabase
      .from('order_items')
      .insert(orderItems);

    if (itemsError) {
      console.error('Error creating order items:', itemsError);
      return {
        success: false,
        orderId: newOrderId,
        message: 'Order created but failed to add items. Please contact support.'
      };
    }

    // Update product stock quantities
    const stockUpdateResult = await updateProductStock(cartItems);
    if (!stockUpdateResult.success) {
      console.error('Error updating product stock:', stockUpdateResult.message);
      // Note: We don't fail the order here as the order is already created
      // In a production system, you might want to implement a rollback mechanism
      console.warn('Order created but stock update failed. Manual intervention may be required.');
    }

    return {
      success: true,
      orderId: newOrderId,
      message: 'Order placed successfully!'
    };
  } catch (error) {
    console.error('Error creating order:', error);
    return {
      success: false,
      message: 'An unexpected error occurred. Please try again.'
    };
  }
}


